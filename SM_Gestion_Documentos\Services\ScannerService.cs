using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Diagnostics;
using System.Management;
using System.Runtime.InteropServices;
using Serilog;
using iTextSharp.text;
using iTextSharp.text.pdf;

namespace SM_Gestion_Documentos.Services
{
    public class ScannerService
    {

        public class ScannerDevice
        {
            public string Id { get; set; } = string.Empty;
            public string Name { get; set; } = string.Empty;
            public string Description { get; set; } = string.Empty;
            public string Type { get; set; } = "Unknown"; // WIA, TWAIN, Network, etc.
        }

        public enum ScanFormat
        {
            JPEG,
            PDF
        }

        public List<ScannerDevice> GetAvailableScanners()
        {
            var scanners = new List<ScannerDevice>();

            try
            {
                Log.Information("Buscando dispositivos de escáner disponibles...");

                // Método 1: Buscar usando TWAIN (más directo para escaneo)
                var twainScanners = GetTWAINScanners();
                scanners.AddRange(twainScanners);

                // Método 2: Buscar usando WIA (Windows Image Acquisition)
                var wiaScanners = GetWIAScanners();
                foreach (var wiaScanner in wiaScanners)
                {
                    // Evitar duplicados
                    if (!scanners.Any(s => s.Name.Equals(wiaScanner.Name, StringComparison.OrdinalIgnoreCase)))
                    {
                        scanners.Add(wiaScanner);
                    }
                }

                // Método 3: Buscar usando WMI (Windows Management Instrumentation)
                var wmiScanners = GetWMIScanners();
                foreach (var wmiScanner in wmiScanners)
                {
                    // Evitar duplicados
                    if (!scanners.Any(s => s.Name.Equals(wmiScanner.Name, StringComparison.OrdinalIgnoreCase)))
                    {
                        scanners.Add(wmiScanner);
                    }
                }

                // Método 3: Verificar aplicaciones de escaneo de Windows disponibles
                if (scanners.Count == 0)
                {
                    // Windows 11: Windows Scan App
                    if (IsWindowsScanAppAvailable())
                    {
                        scanners.Add(new ScannerDevice
                        {
                            Id = "windows_scan_app",
                            Name = "Windows Scan",
                            Description = "Aplicación de escaneo de Windows 11"
                        });
                    }
                    // Windows 10: Windows Fax and Scan
                    else if (IsWindowsFaxAndScanAvailable())
                    {
                        scanners.Add(new ScannerDevice
                        {
                            Id = "windows_fax_scan",
                            Name = "Windows Fax and Scan",
                            Description = "Aplicación de escaneo integrada de Windows"
                        });
                    }
                }

                Log.Information("Se encontraron {Count} dispositivos de escáner", scanners.Count);

                foreach (var scanner in scanners)
                {
                    Log.Information("Scanner encontrado: {Name} - {Description}", scanner.Name, scanner.Description);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al obtener escáneres disponibles");
            }

            return scanners;
        }

        private List<ScannerDevice> GetTWAINScanners()
        {
            var scanners = new List<ScannerDevice>();

            try
            {
                // Por ahora, deshabilitamos TWAIN debido a problemas de compatibilidad
                // TODO: Implementar TWAIN correctamente en una versión futura
                Log.Information("TWAIN temporalmente deshabilitado - usando WIA en su lugar");
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "No se pudieron detectar scanners usando TWAIN");
            }

            return scanners;
        }

        private List<ScannerDevice> GetWIAScanners()
        {
            var scanners = new List<ScannerDevice>();

            try
            {
                // Intentar usar WIA COM para detectar scanners
                Type wiaManagerType = Type.GetTypeFromProgID("WIA.DeviceManager");
                if (wiaManagerType != null)
                {
                    dynamic wiaManager = Activator.CreateInstance(wiaManagerType);
                    dynamic deviceInfos = wiaManager.DeviceInfos;

                    for (int i = 1; i <= deviceInfos.Count; i++)
                    {
                        dynamic deviceInfo = deviceInfos.Item(i);

                        // Verificar si es un scanner (tipo 1)
                        if (deviceInfo.Type == 1)
                        {
                            scanners.Add(new ScannerDevice
                            {
                                Id = deviceInfo.DeviceID,
                                Name = deviceInfo.Properties("Name").Value ?? "Scanner WIA",
                                Description = deviceInfo.Properties("Description").Value ?? "Dispositivo de escáner WIA",
                                Type = "WIA"
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "No se pudieron detectar scanners usando WIA");
            }

            return scanners;
        }

        private List<ScannerDevice> GetWMIScanners()
        {
            var scanners = new List<ScannerDevice>();

            try
            {
                // Buscar dispositivos de imagen usando WMI
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_PnPEntity WHERE Service='usbscan' OR Service='scsiscan' OR Name LIKE '%scanner%' OR Name LIKE '%scan%'"))
                {
                    foreach (ManagementObject device in searcher.Get())
                    {
                        var name = device["Name"]?.ToString();
                        var deviceId = device["DeviceID"]?.ToString();
                        var description = device["Description"]?.ToString();

                        if (!string.IsNullOrEmpty(name) && !string.IsNullOrEmpty(deviceId))
                        {
                            scanners.Add(new ScannerDevice
                            {
                                Id = deviceId,
                                Name = name,
                                Description = description ?? "Dispositivo de escáner detectado por WMI",
                                Type = "WMI"
                            });
                        }
                    }
                }

                // También buscar impresoras multifunción
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_Printer"))
                {
                    foreach (ManagementObject printer in searcher.Get())
                    {
                        var name = printer["Name"]?.ToString();
                        var deviceId = printer["DeviceID"]?.ToString();

                        if (!string.IsNullOrEmpty(name))
                        {
                            scanners.Add(new ScannerDevice
                            {
                                Id = deviceId ?? name,
                                Name = name + " (Multifunción)",
                                Description = "Impresora multifunción con capacidad de escáner",
                                Type = "Multifunción"
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "No se pudieron detectar scanners usando WMI");
            }

            return scanners;
        }

        public async Task<string?> ScanDocumentAsync(string? scannerId = null, string? outputPath = null)
        {
            try
            {
                var availableScanners = GetAvailableScanners();

                if (availableScanners.Count == 0)
                {
                    ShowNoScannersFoundMessage();
                    return null;
                }

                // Si no se especifica un scanner, mostrar lista para seleccionar
                if (string.IsNullOrEmpty(scannerId))
                {
                    var selectionResult = ShowScannerAndFormatSelectionDialog(availableScanners);
                    if (selectionResult == null)
                    {
                        return null; // Usuario canceló
                    }
                    scannerId = selectionResult.Value.ScannerId;
                    var selectedFormat = selectionResult.Value.Format;

                    // Generar nombre de archivo con la extensión correcta
                    if (string.IsNullOrEmpty(outputPath))
                    {
                        var tempDir = Path.Combine(Path.GetTempPath(), "SM_Scanner");
                        Directory.CreateDirectory(tempDir);
                        var extension = selectedFormat == ScanFormat.PDF ? ".pdf" : ".jpg";
                        outputPath = Path.Combine(tempDir, $"scan_{DateTime.Now:yyyyMMdd_HHmmss}{extension}");
                    }
                }

                var selectedScanner = availableScanners.FirstOrDefault(s => s.Id == scannerId);
                if (selectedScanner == null)
                {
                    Log.Warning("Scanner seleccionado no encontrado: {ScannerId}", scannerId);
                    return null;
                }

                Log.Information("Iniciando escaneo con: {ScannerName}", selectedScanner.Name);

                // Si no se especificó outputPath, generar uno por defecto (JPG)
                if (string.IsNullOrEmpty(outputPath))
                {
                    var tempDir = Path.Combine(Path.GetTempPath(), "SM_Scanner");
                    Directory.CreateDirectory(tempDir);
                    outputPath = Path.Combine(tempDir, $"scan_{DateTime.Now:yyyyMMdd_HHmmss}.jpg");
                }

                // Intentar diferentes métodos de escaneo según el tipo de scanner
                return await PerformScan(selectedScanner, outputPath);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error scanning document");
                throw;
            }
        }

        private async Task<string?> PerformScan(ScannerDevice scanner, string outputPath)
        {
            try
            {
                Log.Information("Intentando escanear con: {ScannerName} (ID: {ScannerId})", scanner.Name, scanner.Id);

                // Windows Scan App (Windows 11)
                if (scanner.Id == "windows_scan_app")
                {
                    return await ScanWithWindowsScanApp(outputPath);
                }

                // Windows Fax and Scan (Windows 10)
                if (scanner.Id == "windows_fax_scan")
                {
                    return await ScanWithWindowsFaxAndScan(outputPath);
                }

                // Para scanners TWAIN, usar escaneo directo TWAIN
                if (scanner.Type == "TWAIN" || scanner.Id.StartsWith("twain_"))
                {
                    return await ScanWithTWAIN(scanner, outputPath);
                }

                // Para scanners WIA, usar escaneo directo WIA
                if (scanner.Type == "WIA")
                {
                    return await ScanWithWIA(scanner, outputPath);
                }

                // Para scanners de red Brother, intentar métodos específicos
                if (scanner.Name.ToLower().Contains("brother"))
                {
                    return await ScanWithBrotherNetworkScanner(scanner, outputPath);
                }

                // Para otros scanners, intentar TWAIN primero, luego WIA
                try
                {
                    return await ScanWithTWAIN(scanner, outputPath);
                }
                catch (Exception twainEx)
                {
                    Log.Warning(twainEx, "TWAIN falló, intentando WIA");
                    return await ScanWithWIA(scanner, outputPath);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al realizar escaneo con {ScannerName}", scanner.Name);

                // Fallback: mostrar instrucciones específicas según el tipo de scanner
                ShowEnhancedManualScanInstructions(scanner);
                return null;
            }
        }

        private async Task<string?> ScanWithWindowsScanApp(string outputPath)
        {
            try
            {
                // Intentar abrir la aplicación Windows Scan (Windows 11)
                var processInfo = new ProcessStartInfo
                {
                    FileName = "ms-windows-store://pdp/?productid=9WZDNCRFJ3PV", // Windows Scan App
                    UseShellExecute = true
                };

                // Primero intentar abrir directamente la app
                try
                {
                    Process.Start("ms-scan:");

                    MessageBox.Show(
                        "Se ha abierto la aplicación Windows Scan.\n\n" +
                        "1. Seleccione su scanner de la lista\n" +
                        "2. Configure las opciones de escaneo\n" +
                        "3. Presione 'Escanear'\n" +
                        "4. Guarde la imagen en el escritorio\n" +
                        "5. Presione OK cuando termine\n\n" +
                        "Luego use 'Seleccionar Archivo' para cargar la imagen escaneada.",
                        "Escaneo con Windows Scan",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);

                    Log.Information("Windows Scan App abierto exitosamente");
                    return null;
                }
                catch
                {
                    // Si no está instalado, intentar abrir la Store para instalarlo
                    Process.Start(processInfo);

                    MessageBox.Show(
                        "La aplicación Windows Scan no está instalada.\n\n" +
                        "Se ha abierto Microsoft Store para instalarla.\n" +
                        "Una vez instalada, podrá usar esta función.\n\n" +
                        "Por ahora, use el software del fabricante del scanner\n" +
                        "y luego 'Seleccionar Archivo' para cargar la imagen.",
                        "Instalar Windows Scan",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);

                    return null;
                }
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "No se pudo abrir Windows Scan App");
                throw;
            }
        }

        private async Task<string?> ScanWithWindowsFaxAndScan(string outputPath)
        {
            try
            {
                var processInfo = new ProcessStartInfo
                {
                    FileName = "WFS.exe",
                    Arguments = "/scan",
                    UseShellExecute = true
                };

                using var process = Process.Start(processInfo);
                if (process != null)
                {
                    MessageBox.Show(
                        "Se ha abierto Windows Fax and Scan.\n\n" +
                        "1. Realice el escaneo del documento\n" +
                        "2. Guarde la imagen en el escritorio\n" +
                        "3. Presione OK cuando termine\n\n" +
                        "Luego use 'Seleccionar Archivo' para cargar la imagen escaneada.",
                        "Escaneo de Documento",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);

                    Log.Information("Windows Fax and Scan abierto exitosamente");
                    return null;
                }
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "No se pudo abrir Windows Fax and Scan");
                throw;
            }

            return null;
        }

        private async Task<string?> ScanWithTWAIN(ScannerDevice scanner, string outputPath)
        {
            // TWAIN temporalmente deshabilitado debido a problemas de compatibilidad
            Log.Warning("TWAIN no está disponible, redirigiendo a WIA");
            return await ScanWithWIA(scanner, outputPath);
        }

        private async Task<string?> ScanWithBrotherNetworkScanner(ScannerDevice scanner, string outputPath)
        {
            try
            {
                Log.Information("Intentando escaneo con scanner Brother de red: {ScannerName}", scanner.Name);

                // Intentar usar WIA primero
                try
                {
                    return await ScanWithWIA(scanner, outputPath);
                }
                catch (Exception wiaEx)
                {
                    Log.Warning(wiaEx, "WIA falló para scanner Brother, intentando método alternativo");
                }

                // Si WIA falla, mostrar instrucciones específicas para Brother
                ShowBrotherNetworkScanInstructions(scanner);
                return null;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al escanear con scanner Brother de red");
                throw;
            }
        }

        private async Task<string?> ScanWithWIA(ScannerDevice scanner, string outputPath)
        {
            try
            {
                Log.Information("Iniciando escaneo WIA con: {ScannerName}", scanner.Name);

                // Determinar si el archivo de salida debe ser PDF
                var isPDF = outputPath.ToLower().EndsWith(".pdf");
                var tempImagePath = isPDF ? Path.ChangeExtension(outputPath, ".jpg") : outputPath;

                // Método 1: Intentar escaneo directo con WIA
                try
                {
                    Type wiaManagerType = Type.GetTypeFromProgID("WIA.DeviceManager");
                    if (wiaManagerType != null)
                    {
                        dynamic wiaManager = Activator.CreateInstance(wiaManagerType);

                        // Intentar conectar al dispositivo específico
                        dynamic device = null;
                        try
                        {
                            device = wiaManager.DeviceInfos(scanner.Id).Connect();
                        }
                        catch
                        {
                            // Si falla con el ID específico, intentar buscar por nombre
                            foreach (dynamic deviceInfo in wiaManager.DeviceInfos)
                            {
                                if (deviceInfo.Properties("Name").Value.ToString().Contains(scanner.Name.Split(' ')[0]))
                                {
                                    device = deviceInfo.Connect();
                                    break;
                                }
                            }
                        }

                        if (device != null)
                        {
                            // Mostrar diálogo de escaneo de WIA
                            Type wiaCommonDialogType = Type.GetTypeFromProgID("WIA.CommonDialog");
                            if (wiaCommonDialogType != null)
                            {
                                dynamic commonDialog = Activator.CreateInstance(wiaCommonDialogType);
                                dynamic image = commonDialog.ShowAcquireImage(device);

                                if (image != null)
                                {
                                    // Guardar como imagen temporal
                                    image.SaveFile(tempImagePath);

                                    // Si se requiere PDF, convertir la imagen
                                    if (isPDF)
                                    {
                                        var finalPath = await ConvertImageToPDF(tempImagePath, outputPath);
                                        if (finalPath != null)
                                        {
                                            Log.Information("Documento escaneado guardado como PDF en: {OutputPath}", outputPath);
                                            return finalPath;
                                        }
                                    }
                                    else
                                    {
                                        Log.Information("Imagen escaneada guardada exitosamente en: {OutputPath}", outputPath);
                                        return outputPath;
                                    }
                                }
                            }
                        }
                    }
                }
                catch (Exception wiaEx)
                {
                    Log.Warning(wiaEx, "Escaneo WIA directo falló, intentando método alternativo");
                }

                // Método 2: Usar diálogo genérico de WIA
                try
                {
                    Type wiaCommonDialogType = Type.GetTypeFromProgID("WIA.CommonDialog");
                    if (wiaCommonDialogType != null)
                    {
                        dynamic commonDialog = Activator.CreateInstance(wiaCommonDialogType);

                        // Mostrar diálogo para seleccionar scanner y escanear
                        dynamic image = commonDialog.ShowAcquireImage();

                        if (image != null)
                        {
                            // Guardar como imagen temporal
                            image.SaveFile(tempImagePath);

                            // Si se requiere PDF, convertir la imagen
                            if (isPDF)
                            {
                                var finalPath = await ConvertImageToPDF(tempImagePath, outputPath);
                                if (finalPath != null)
                                {
                                    Log.Information("Documento escaneado guardado como PDF con diálogo genérico en: {OutputPath}", outputPath);
                                    return finalPath;
                                }
                            }
                            else
                            {
                                Log.Information("Imagen escaneada guardada con diálogo genérico en: {OutputPath}", outputPath);
                                return outputPath;
                            }
                        }
                    }
                }
                catch (Exception genericEx)
                {
                    Log.Warning(genericEx, "Diálogo genérico WIA también falló");
                }

                // Si llegamos aquí, WIA no funcionó
                throw new InvalidOperationException("No se pudo inicializar el escaneo WIA");
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al escanear con WIA: {ScannerName}", scanner.Name);
                throw;
            }
        }

        public async Task<string?> ScanDocumentWithUIAsync()
        {
            try
            {
                return await ScanDocumentAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al escanear documento con interfaz");
                throw;
            }
        }

        public bool IsScannerAvailable()
        {
            try
            {
                var scanners = GetAvailableScanners();
                return scanners.Count > 0;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al verificar disponibilidad de scanner");
                return false;
            }
        }

        private (string ScannerId, ScanFormat Format)? ShowScannerAndFormatSelectionDialog(List<ScannerDevice> scanners)
        {
            try
            {
                using (var form = new Form())
                {
                    form.Text = "Configurar Escaneo";
                    form.Size = new Size(550, 450);
                    form.StartPosition = FormStartPosition.CenterParent;
                    form.FormBorderStyle = FormBorderStyle.FixedDialog;
                    form.MaximizeBox = false;
                    form.MinimizeBox = false;

                    // Label para scanner
                    var labelScanner = new Label
                    {
                        Text = "Seleccione el scanner:",
                        Location = new Point(10, 10),
                        Size = new Size(520, 20),
                        AutoSize = false
                    };

                    // ListView para scanners
                    var listView = new ListView
                    {
                        Location = new Point(10, 35),
                        Size = new Size(520, 200),
                        View = View.Details,
                        FullRowSelect = true,
                        GridLines = true,
                        MultiSelect = false
                    };

                    listView.Columns.Add("Nombre", 200);
                    listView.Columns.Add("Tipo", 80);
                    listView.Columns.Add("Descripción", 230);

                    foreach (var scanner in scanners)
                    {
                        var item = new ListViewItem(scanner.Name);
                        item.SubItems.Add(scanner.Type);
                        item.SubItems.Add(scanner.Description);
                        item.Tag = scanner.Id;
                        listView.Items.Add(item);
                    }

                    if (listView.Items.Count > 0)
                    {
                        listView.Items[0].Selected = true;
                    }

                    // Label para formato
                    var labelFormat = new Label
                    {
                        Text = "Seleccione el formato de salida:",
                        Location = new Point(10, 250),
                        Size = new Size(520, 20),
                        AutoSize = false
                    };

                    // RadioButtons para formato
                    var radioJPG = new RadioButton
                    {
                        Text = "Imagen JPG (recomendado para fotos)",
                        Location = new Point(20, 275),
                        Size = new Size(300, 25),
                        Checked = true
                    };

                    var radioPDF = new RadioButton
                    {
                        Text = "Documento PDF (recomendado para documentos)",
                        Location = new Point(20, 305),
                        Size = new Size(350, 25)
                    };

                    // Información adicional
                    var labelInfo = new Label
                    {
                        Text = "• JPG: Mejor para fotografías y imágenes en color\n• PDF: Mejor para documentos de texto, mantiene calidad y es más compacto",
                        Location = new Point(20, 335),
                        Size = new Size(500, 40),
                        AutoSize = false,
                        ForeColor = Color.DarkBlue
                    };

                    // Botones
                    var btnOK = new Button
                    {
                        Text = "Escanear",
                        Location = new Point(370, 385),
                        Size = new Size(80, 30),
                        DialogResult = DialogResult.OK
                    };

                    var btnCancel = new Button
                    {
                        Text = "Cancelar",
                        Location = new Point(460, 385),
                        Size = new Size(80, 30),
                        DialogResult = DialogResult.Cancel
                    };

                    form.Controls.AddRange(new Control[] {
                        labelScanner, listView, labelFormat, radioJPG, radioPDF, labelInfo, btnOK, btnCancel
                    });
                    form.AcceptButton = btnOK;
                    form.CancelButton = btnCancel;

                    if (form.ShowDialog() == DialogResult.OK && listView.SelectedItems.Count > 0)
                    {
                        var scannerId = listView.SelectedItems[0].Tag?.ToString();
                        var format = radioPDF.Checked ? ScanFormat.PDF : ScanFormat.JPEG;

                        if (!string.IsNullOrEmpty(scannerId))
                        {
                            return (scannerId, format);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al mostrar diálogo de selección de scanner y formato");
            }

            return null;
        }

        private string? ShowScannerSelectionDialog(List<ScannerDevice> scanners)
        {
            try
            {
                using (var form = new Form())
                {
                    form.Text = "Seleccionar Scanner";
                    form.Size = new Size(500, 350);
                    form.StartPosition = FormStartPosition.CenterParent;
                    form.FormBorderStyle = FormBorderStyle.FixedDialog;
                    form.MaximizeBox = false;
                    form.MinimizeBox = false;

                    var label = new Label
                    {
                        Text = "Seleccione el scanner que desea usar:",
                        Location = new Point(10, 10),
                        Size = new Size(460, 30),
                        AutoSize = false
                    };

                    var listView = new ListView
                    {
                        Location = new Point(10, 50),
                        Size = new Size(460, 200),
                        View = View.Details,
                        FullRowSelect = true,
                        GridLines = true,
                        MultiSelect = false
                    };

                    listView.Columns.Add("Nombre", 180);
                    listView.Columns.Add("Tipo", 80);
                    listView.Columns.Add("Descripción", 180);

                    foreach (var scanner in scanners)
                    {
                        var item = new ListViewItem(scanner.Name);
                        item.SubItems.Add(scanner.Type);
                        item.SubItems.Add(scanner.Description);
                        item.Tag = scanner.Id;
                        listView.Items.Add(item);
                    }

                    if (listView.Items.Count > 0)
                    {
                        listView.Items[0].Selected = true;
                    }

                    var btnOK = new Button
                    {
                        Text = "Aceptar",
                        Location = new Point(320, 270),
                        Size = new Size(80, 30),
                        DialogResult = DialogResult.OK
                    };

                    var btnCancel = new Button
                    {
                        Text = "Cancelar",
                        Location = new Point(410, 270),
                        Size = new Size(80, 30),
                        DialogResult = DialogResult.Cancel
                    };

                    form.Controls.AddRange(new Control[] { label, listView, btnOK, btnCancel });
                    form.AcceptButton = btnOK;
                    form.CancelButton = btnCancel;

                    if (form.ShowDialog() == DialogResult.OK && listView.SelectedItems.Count > 0)
                    {
                        return listView.SelectedItems[0].Tag?.ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al mostrar diálogo de selección de scanner");
            }

            return null;
        }

        private void ShowNoScannersFoundMessage()
        {
            var message = "No se encontraron scanners disponibles en el sistema.\n\n" +
                         "Posibles soluciones:\n" +
                         "• Verifique que el scanner esté conectado y encendido\n" +
                         "• Para scanners de red, asegúrese de que estén configurados correctamente\n" +
                         "• Instale los drivers más recientes del fabricante\n" +
                         "• Reinicie el servicio de Windows Image Acquisition (WIA)\n\n" +
                         "Como alternativa, puede usar el software del fabricante para escanear\n" +
                         "y luego usar 'Seleccionar Archivo' para cargar la imagen.";

            MessageBox.Show(message, "Scanner no disponible", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        private void ShowBrotherNetworkScanInstructions(ScannerDevice scanner)
        {
            var message = $"Scanner Brother detectado: '{scanner.Name}'\n\n" +
                         "Para scanners Brother de red, recomendamos:\n\n" +
                         "OPCIÓN 1 - Brother iPrint&Scan:\n" +
                         "1. Descargue 'Brother iPrint&Scan' desde la Microsoft Store\n" +
                         "2. Configure su scanner en la aplicación\n" +
                         "3. Use la aplicación para escanear\n\n" +
                         "OPCIÓN 2 - Panel del Scanner:\n" +
                         "1. Vaya al panel frontal del scanner\n" +
                         "2. Seleccione 'Scan to PC' o 'Escanear a PC'\n" +
                         "3. Seleccione su computadora de la lista\n" +
                         "4. El archivo se guardará automáticamente\n\n" +
                         "OPCIÓN 3 - Interfaz Web:\n" +
                         "1. Abra un navegador web\n" +
                         "2. Vaya a la IP del scanner (ej: *************)\n" +
                         "3. Use la función de escaneo web\n\n" +
                         "Luego use 'Seleccionar Archivo' para cargar la imagen.";

            MessageBox.Show(message, "Instrucciones Scanner Brother", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowEnhancedManualScanInstructions(ScannerDevice scanner)
        {
            var message = $"No se pudo iniciar el escaneo automático con '{scanner.Name}'.\n\n";

            // Instrucciones específicas según el fabricante
            if (scanner.Name.ToLower().Contains("brother"))
            {
                ShowBrotherNetworkScanInstructions(scanner);
                return;
            }
            else if (scanner.Name.ToLower().Contains("hp"))
            {
                message += "Para scanners HP:\n" +
                          "1. Use 'HP Smart' desde Microsoft Store\n" +
                          "2. O use el software HP instalado en su PC\n\n";
            }
            else if (scanner.Name.ToLower().Contains("canon"))
            {
                message += "Para scanners Canon:\n" +
                          "1. Use 'Canon PRINT' desde Microsoft Store\n" +
                          "2. O use 'IJ Scan Utility' si está instalado\n\n";
            }
            else if (scanner.Name.ToLower().Contains("epson"))
            {
                message += "Para scanners Epson:\n" +
                          "1. Use 'Epson Smart Panel' desde Microsoft Store\n" +
                          "2. O use 'Epson Scan 2' si está instalado\n\n";
            }

            message += "Pasos generales:\n" +
                      "1. Use el software del fabricante del scanner\n" +
                      "2. Guarde la imagen en el escritorio\n" +
                      "3. Use 'Seleccionar Archivo' para cargar la imagen\n\n" +
                      "Esto es común con scanners de red que requieren\n" +
                      "software específico del fabricante.";

            MessageBox.Show(message, "Escaneo Manual Requerido", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private bool IsWindowsScanAppAvailable()
        {
            try
            {
                // Verificar si Windows Scan App está disponible (Windows 11)
                // Intentar verificar si la app está instalada
                var processInfo = new ProcessStartInfo
                {
                    FileName = "powershell.exe",
                    Arguments = "-Command \"Get-AppxPackage -Name '*WindowsScan*' | Select-Object Name\"",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(processInfo))
                {
                    if (process != null)
                    {
                        var output = process.StandardOutput.ReadToEnd();
                        process.WaitForExit();
                        return !string.IsNullOrEmpty(output) && output.Contains("WindowsScan");
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "No se pudo verificar Windows Scan App");
            }

            // Fallback: verificar si estamos en Windows 11
            try
            {
                var version = Environment.OSVersion.Version;
                return version.Major >= 10 && version.Build >= 22000; // Windows 11 build number
            }
            catch
            {
                return false;
            }
        }

        private bool IsWindowsFaxAndScanAvailable()
        {
            try
            {
                // Verificar si Windows Fax and Scan está disponible
                var wfsPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.System), "WFS.exe");
                return File.Exists(wfsPath);
            }
            catch
            {
                return false;
            }
        }

        private async Task<string?> ConvertImageToPDF(string imagePath, string pdfPath)
        {
            try
            {
                Log.Information("Convirtiendo imagen a PDF: {ImagePath} -> {PdfPath}", imagePath, pdfPath);

                using (var image = System.Drawing.Image.FromFile(imagePath))
                {
                    // Crear documento PDF usando iTextSharp
                    using (var document = new iTextSharp.text.Document())
                    {
                        using (var stream = new FileStream(pdfPath, FileMode.Create))
                        {
                            var writer = iTextSharp.text.pdf.PdfWriter.GetInstance(document, stream);
                            document.Open();

                            // Convertir imagen a formato compatible con iTextSharp
                            using (var ms = new MemoryStream())
                            {
                                image.Save(ms, ImageFormat.Jpeg);
                                var imageBytes = ms.ToArray();

                                var iTextImage = iTextSharp.text.Image.GetInstance(imageBytes);

                                // Ajustar el tamaño de la imagen al documento
                                var pageSize = document.PageSize;
                                var margin = 36; // 0.5 inch margin
                                var availableWidth = pageSize.Width - (2 * margin);
                                var availableHeight = pageSize.Height - (2 * margin);

                                // Escalar la imagen para que quepa en la página
                                iTextImage.ScaleToFit(availableWidth, availableHeight);

                                // Centrar la imagen
                                iTextImage.SetAbsolutePosition(
                                    (pageSize.Width - iTextImage.ScaledWidth) / 2,
                                    (pageSize.Height - iTextImage.ScaledHeight) / 2
                                );

                                document.Add(iTextImage);
                            }

                            document.Close();
                        }
                    }
                }

                // Eliminar archivo temporal de imagen
                try
                {
                    if (File.Exists(imagePath) && imagePath != pdfPath)
                    {
                        File.Delete(imagePath);
                    }
                }
                catch (Exception deleteEx)
                {
                    Log.Warning(deleteEx, "No se pudo eliminar archivo temporal: {ImagePath}", imagePath);
                }

                Log.Information("Conversión a PDF completada exitosamente");
                return pdfPath;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al convertir imagen a PDF");
                return null;
            }
        }

        public void Dispose()
        {
            // No hay recursos específicos que liberar por ahora
            Log.Information("ScannerService disposed");
        }
    }
}
