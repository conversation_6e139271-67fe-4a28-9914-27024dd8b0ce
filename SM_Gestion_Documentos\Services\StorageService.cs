﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Serilog;
using System.IO;
using SM_Gestion_Documentos.Configuration;

namespace SM_Gestion_Documentos.Services
{
    public class StorageService
    {
        private readonly string _localStoragePath;
        private readonly string _networkStoragePath;
        public string CurrentStorageType { get; private set; } = "Local";

        public StorageService()
        {
            // Obtener configuración desde App.config
            _localStoragePath = AppConfig.LocalStoragePath;
            _networkStoragePath = AppConfig.NetworkStoragePath;
            CurrentStorageType = AppConfig.StorageType;

            EnsureDirectoryExists(_localStoragePath);
            Log.Information("StorageService inicializado - Tipo: {StorageType}, Ruta: {StoragePath}", CurrentStorageType, _localStoragePath);
        }

        public void SetStorageType(string storageType)
        {
            CurrentStorageType = storageType;
            Log.Information("Tipo de almacenamiento cambiado a: {StorageType}", storageType);
        }

        public async Task<string?> StoreFileAsync(string sourceFilePath, string dni, string fileName)
        {
            try
            {
                // Validar archivo antes de almacenar
                if (!ValidateFile(sourceFilePath))
                {
                    Log.Warning("Archivo no válido para almacenar: {SourcePath}", sourceFilePath);
                    return null;
                }

                var destinationPath = GetDestinationPath(dni, fileName);
                var destinationDir = Path.GetDirectoryName(destinationPath);

                if (!string.IsNullOrEmpty(destinationDir))
                {
                    EnsureDirectoryExists(destinationDir);
                }

                switch (CurrentStorageType.ToLower())
                {
                    case "local":
                        return await StoreFileLocallyAsync(sourceFilePath, destinationPath);

                    case "network":
                        return await StoreFileOnNetworkAsync(sourceFilePath, destinationPath);

                    case "googledrive":
                        return await StoreFileOnGoogleDriveAsync(sourceFilePath, dni, fileName);

                    case "onedrive":
                        return await StoreFileOnOneDriveAsync(sourceFilePath, dni, fileName);

                    default:
                        Log.Warning("Tipo de almacenamiento desconocido: {StorageType}, usando almacenamiento local", CurrentStorageType);
                        return await StoreFileLocallyAsync(sourceFilePath, destinationPath);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al almacenar archivo: {SourcePath}", sourceFilePath);
                return null;
            }
        }

        private async Task<string?> StoreFileLocallyAsync(string sourceFilePath, string destinationPath)
        {
            try
            {
                await Task.Run(() => File.Copy(sourceFilePath, destinationPath, true));
                Log.Information("Archivo almacenado localmente: {DestinationPath}", destinationPath);
                return destinationPath;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al almacenar archivo localmente: {SourcePath} -> {DestinationPath}",
                    sourceFilePath, destinationPath);
                return null;
            }
        }

        private async Task<string?> StoreFileOnNetworkAsync(string sourceFilePath, string destinationPath)
        {
            try
            {
                var networkPath = destinationPath.Replace(_localStoragePath, _networkStoragePath);
                var networkDir = Path.GetDirectoryName(networkPath);

                if (!string.IsNullOrEmpty(networkDir))
                {
                    EnsureDirectoryExists(networkDir);
                }

                await Task.Run(() => File.Copy(sourceFilePath, networkPath, true));
                Log.Information("Archivo almacenado en red: {NetworkPath}", networkPath);
                return networkPath;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al almacenar archivo en red: {SourcePath}", sourceFilePath);
                return null;
            }
        }

        private async Task<string?> StoreFileOnGoogleDriveAsync(string sourceFilePath, string dni, string fileName)
        {
            try
            {
                // Implementación básica para Google Drive
                // Requiere configuración de credenciales OAuth2
                Log.Information("Almacenamiento de Google Drive no completamente implementado. Usando almacenamiento local.");
                return await StoreFileLocallyAsync(sourceFilePath, GetDestinationPath(dni, fileName));
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al almacenar archivo en Google Drive: {SourcePath}", sourceFilePath);
                return null;
            }
        }

        private async Task<string?> StoreFileOnOneDriveAsync(string sourceFilePath, string dni, string fileName)
        {
            try
            {
                // Implementación básica para OneDrive
                // Requiere configuración de Microsoft Graph API
                Log.Information("Almacenamiento de OneDrive no completamente implementado. Usando almacenamiento local.");
                return await StoreFileLocallyAsync(sourceFilePath, GetDestinationPath(dni, fileName));
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al almacenar archivo en OneDrive: {SourcePath}", sourceFilePath);
                return null;
            }
        }

        public async Task<bool> DeleteFileAsync(string filePath, string storageType)
        {
            try
            {
                switch (storageType.ToLower())
                {
                    case "local":
                    case "network":
                        if (File.Exists(filePath))
                        {
                            await Task.Run(() => File.Delete(filePath));
                            Log.Information("Archivo eliminado: {FilePath}", filePath);
                            return true;
                        }
                        break;

                    case "googledrive":
                        // Implementar eliminación en Google Drive
                        Log.Information("Eliminación de archivos de Google Drive no implementada");
                        break;

                    case "onedrive":
                        // Implementar eliminación en OneDrive
                        Log.Information("Eliminación de archivos de OneDrive no implementada");
                        break;
                }

                return false;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al eliminar archivo: {FilePath}", filePath);
                return false;
            }
        }

        public async Task<string?> GetFilePathAsync(string storedPath, string storageType)
        {
            try
            {
                switch (storageType.ToLower())
                {
                    case "local":
                    case "network":
                        return File.Exists(storedPath) ? storedPath : null;

                    case "googledrive":
                        // Implementar descarga temporal desde Google Drive
                        return await DownloadFromCloudAsync(storedPath, "googledrive");

                    case "onedrive":
                        // Implementar descarga temporal desde OneDrive
                        return await DownloadFromCloudAsync(storedPath, "onedrive");

                    default:
                        return null;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al obtener ruta de archivo: {StoredPath}", storedPath);
                return null;
            }
        }

        private async Task<string?> DownloadFromCloudAsync(string cloudPath, string provider)
        {
            try
            {
                // Crear directorio temporal
                var tempDir = Path.Combine(Path.GetTempPath(), "DocumentManager");
                EnsureDirectoryExists(tempDir);

                var tempFile = Path.Combine(tempDir, Path.GetFileName(cloudPath));

                // Aquí implementarías la descarga real desde el proveedor de nube
                Log.Information("Descarga de nube no completamente implementada para {Provider}", provider);

                return tempFile;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al descargar de la nube: {CloudPath}", cloudPath);
                return null;
            }
        }

        private string GetDestinationPath(string dni, string fileName)
        {
            var year = DateTime.Now.Year.ToString();
            var month = DateTime.Now.Month.ToString("00");

            var basePath = CurrentStorageType.ToLower() == "network" ? _networkStoragePath : _localStoragePath;

            return Path.Combine(basePath, dni, year, month, fileName);
        }

        private void EnsureDirectoryExists(string path)
        {
            try
            {
                if (!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al crear directorio: {Path}", path);
            }
        }

        public List<string> GetAvailableStorageTypes()
        {
            var types = new List<string> { "Local" };

            // Verificar disponibilidad de almacenamiento en red
            try
            {
                if (Directory.Exists(Path.GetDirectoryName(_networkStoragePath)))
                {
                    types.Add("Network");
                }
            }
            catch
            {
                // Red no disponible
            }

            // Agregar opciones de nube (requieren configuración)
            types.Add("GoogleDrive");
            types.Add("OneDrive");

            return types;
        }

        public async Task<bool> MigrateDocumentStructureAsync()
        {
            try
            {
                Log.Information("Iniciando migración de estructura de documentos de Año/Mes/DNI a DNI/Año/Mes");

                var basePath = CurrentStorageType.ToLower() == "network" ? _networkStoragePath : _localStoragePath;

                if (!Directory.Exists(basePath))
                {
                    Log.Warning("La ruta base de almacenamiento no existe: {BasePath}", basePath);
                    return false;
                }

                var migrationCount = 0;
                var errorCount = 0;

                // Buscar estructura antigua: Year/Month/DNI
                var yearDirectories = Directory.GetDirectories(basePath)
                    .Where(d => Path.GetFileName(d).Length == 4 && int.TryParse(Path.GetFileName(d), out _))
                    .ToList();

                foreach (var yearDir in yearDirectories)
                {
                    var year = Path.GetFileName(yearDir);
                    var monthDirectories = Directory.GetDirectories(yearDir)
                        .Where(d => Path.GetFileName(d).Length == 2 && int.TryParse(Path.GetFileName(d), out _))
                        .ToList();

                    foreach (var monthDir in monthDirectories)
                    {
                        var month = Path.GetFileName(monthDir);
                        var dniDirectories = Directory.GetDirectories(monthDir);

                        foreach (var dniDir in dniDirectories)
                        {
                            var dni = Path.GetFileName(dniDir);
                            var files = Directory.GetFiles(dniDir);

                            if (files.Length > 0)
                            {
                                // Crear nueva estructura: DNI/Year/Month
                                var newDniPath = Path.Combine(basePath, dni);
                                var newYearPath = Path.Combine(newDniPath, year);
                                var newMonthPath = Path.Combine(newYearPath, month);

                                try
                                {
                                    EnsureDirectoryExists(newMonthPath);

                                    // Mover archivos
                                    foreach (var file in files)
                                    {
                                        var fileName = Path.GetFileName(file);
                                        var newFilePath = Path.Combine(newMonthPath, fileName);

                                        if (!File.Exists(newFilePath))
                                        {
                                            File.Move(file, newFilePath);
                                            migrationCount++;
                                            Log.Information("Archivo migrado: {OldPath} -> {NewPath}", file, newFilePath);
                                        }
                                        else
                                        {
                                            Log.Warning("El archivo ya existe en el destino: {NewPath}", newFilePath);
                                        }
                                    }

                                    // Eliminar directorio vacío si no tiene más archivos
                                    if (Directory.GetFiles(dniDir).Length == 0)
                                    {
                                        Directory.Delete(dniDir);
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Log.Error(ex, "Error al migrar archivos desde {OldPath}", dniDir);
                                    errorCount++;
                                }
                            }
                        }

                        // Eliminar directorio de mes si está vacío
                        try
                        {
                            if (Directory.GetDirectories(monthDir).Length == 0 && Directory.GetFiles(monthDir).Length == 0)
                            {
                                Directory.Delete(monthDir);
                            }
                        }
                        catch (Exception ex)
                        {
                            Log.Warning(ex, "No se pudo eliminar el directorio de mes vacío: {MonthDir}", monthDir);
                        }
                    }

                    // Eliminar directorio de año si está vacío
                    try
                    {
                        if (Directory.GetDirectories(yearDir).Length == 0 && Directory.GetFiles(yearDir).Length == 0)
                        {
                            Directory.Delete(yearDir);
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Warning(ex, "No se pudo eliminar el directorio de año vacío: {YearDir}", yearDir);
                    }
                }

                Log.Information("Migración de estructura de documentos completada. Archivos migrados: {MigrationCount}, Errores: {ErrorCount}",
                    migrationCount, errorCount);

                return errorCount == 0;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error durante la migración de estructura de documentos");
                return false;
            }
        }

        private bool ValidateFile(string filePath)
        {
            try
            {
                // Verificar que el archivo existe
                if (!File.Exists(filePath))
                {
                    Log.Warning("Archivo no encontrado: {FilePath}", filePath);
                    return false;
                }

                // Verificar extensión de archivo
                var extension = Path.GetExtension(filePath).ToLower();
                var allowedExtensions = AppConfig.AllowedFileExtensions;

                if (!allowedExtensions.Contains(extension))
                {
                    Log.Warning("Extensión de archivo no permitida: {Extension}. Permitidas: {AllowedExtensions}",
                        extension, string.Join(", ", allowedExtensions));
                    return false;
                }

                // Verificar tamaño de archivo
                var fileInfo = new FileInfo(filePath);
                var maxSizeBytes = AppConfig.MaxFileSizeMB * 1024 * 1024; // Convertir MB a bytes

                if (fileInfo.Length > maxSizeBytes)
                {
                    Log.Warning("Archivo excede el tamaño máximo permitido: {FileSize}MB > {MaxSize}MB",
                        fileInfo.Length / (1024 * 1024), AppConfig.MaxFileSizeMB);
                    return false;
                }

                Log.Debug("Archivo validado exitosamente: {FilePath} ({FileSize}MB)",
                    filePath, fileInfo.Length / (1024 * 1024));
                return true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al validar archivo: {FilePath}", filePath);
                return false;
            }
        }
    }
}
