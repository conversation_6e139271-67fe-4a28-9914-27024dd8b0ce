﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Tesseract;
using System.Drawing;
using System.Text.RegularExpressions;
using Serilog;
using iTextSharp.text.pdf;
using iTextSharp.text.pdf.parser;
using System.IO;
using Docnet.Core;
using Docnet.Core.Models;
using SM_Gestion_Documentos.Configuration;
using System.Net.Http;
using System.Net.Http;

namespace SM_Gestion_Documentos.Services
{
    public class OCRService : IDisposable
    {
        private TesseractEngine? _engine;
        private readonly string _tessDataPath;

        public OCRService()
        {
            // Obtener configuración desde App.config
            var configTessDataPath = AppConfig.TessDataPath;

            // Si es ruta relativa, usar la carpeta de la aplicación
            if (!System.IO.Path.IsPathRooted(configTessDataPath))
            {
                _tessDataPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, configTessDataPath);
            }
            else
            {
                _tessDataPath = configTessDataPath;
            }

            InitializeEngine();
        }

        private void InitializeEngine()
        {
            try
            {
                if (!Directory.Exists(_tessDataPath))
                {
                    Directory.CreateDirectory(_tessDataPath);
                    Log.Information("Directorio tessdata creado: {TessDataPath}", _tessDataPath);
                }

                var language = AppConfig.OCRLanguage;
                var languageFile = System.IO.Path.Combine(_tessDataPath, $"{language}.traineddata");

                // Verificar si existe el archivo de idioma
                if (!File.Exists(languageFile))
                {
                    Log.Warning("Archivo de idioma OCR no encontrado: {LanguageFile}", languageFile);

                    // Intentar descargar automáticamente
                    var downloadTask = Task.Run(() => DownloadTessDataFileAsync(language, languageFile));
                    if (downloadTask.Result)
                    {
                        Log.Information("Archivo de idioma OCR descargado exitosamente: {LanguageFile}", languageFile);
                    }
                    else
                    {
                        Log.Error("No se pudo descargar el archivo de idioma OCR. El OCR no funcionará correctamente.");
                        throw new FileNotFoundException($"Archivo de idioma OCR no encontrado: {languageFile}");
                    }
                }

                _engine = new TesseractEngine(_tessDataPath, language, EngineMode.Default);
                _engine.SetVariable("tessedit_char_whitelist", "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz ");

                Log.Information("Motor OCR inicializado correctamente - Idioma: {Language}, TessData: {TessDataPath}",
                    language, _tessDataPath);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al inicializar el motor OCR");
                throw;
            }
        }

        public async Task<string?> ExtractDNIFromImageAsync(string imagePath)
        {
            try
            {
                Log.Information("Iniciando extracción de DNI desde archivo: {ImagePath}", imagePath);

                // Verificar si es un PDF escaneado
                if (imagePath.ToLower().EndsWith(".pdf"))
                {
                    return await ExtractDNIFromScannedPDF(imagePath);
                }

                // Procesar imagen normal
                return await ProcessImageWithOCR(imagePath);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al extraer DNI del archivo: {ImagePath}", imagePath);
                return null;
            }
        }

        private async Task<string?> ProcessImageWithOCR(string imagePath)
        {
            try
            {
                if (_engine == null)
                {
                    Log.Error("Motor OCR no inicializado");
                    return null;
                }

                Log.Information("Procesando imagen con OCR: {ImagePath}", imagePath);

                // Cargar imagen original
                using var originalImg = Pix.LoadFromFile(imagePath);

                // Crear versiones procesadas de la imagen para mejorar OCR
                var processedImages = new List<Pix>();

                try
                {
                    // Imagen 1: Original
                    processedImages.Add(originalImg.Clone());

                    // Imagen 2: Escala de grises si es necesario
                    var grayImg = originalImg.Clone();
                    if (grayImg.Depth > 8)
                    {
                        grayImg = grayImg.ConvertRGBToGray(0.299f, 0.587f, 0.114f);
                    }
                    processedImages.Add(grayImg);

                    Log.Information("Creadas {Count} versiones procesadas de la imagen para OCR", processedImages.Count);

                    // Configuraciones de OCR optimizadas para documentos escaneados
                    var configurations = new[]
                    {
                        // Configuración 1: Estándar para documentos
                        new { Name = "Estándar", Mode = EngineMode.Default, Whitelist = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz .:,-", PSM = PageSegMode.Auto },
                        // Configuración 2: Solo números y letras para DNI
                        new { Name = "DNI", Mode = EngineMode.Default, Whitelist = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ ", PSM = PageSegMode.SingleBlock },
                        // Configuración 3: Bloque único para DNI específico
                        new { Name = "Bloque", Mode = EngineMode.Default, Whitelist = "0123456789", PSM = PageSegMode.SingleBlock },
                        // Configuración 4: Más agresivo para documentos difíciles
                        new { Name = "Agresivo", Mode = EngineMode.TesseractAndLstm, Whitelist = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz .:,-/\\()[]", PSM = PageSegMode.Auto }
                    };

                    // Probar cada combinación de imagen procesada + configuración OCR
                    for (int imgIndex = 0; imgIndex < processedImages.Count; imgIndex++)
                    {
                        var img = processedImages[imgIndex];
                        var imgType = new[] { "Original", "Escala de grises" }[imgIndex];

                        foreach (var config in configurations)
                        {
                            try
                            {
                                Log.Debug("Probando imagen {ImageType} con configuración {ConfigName}", imgType, config.Name);

                                // Configurar motor para esta iteración
                                _engine.SetVariable("tessedit_char_whitelist", config.Whitelist);
                                _engine.SetVariable("preserve_interword_spaces", "1");
                                _engine.SetVariable("tessedit_pageseg_mode", ((int)config.PSM).ToString());

                                using var page = _engine.Process(img);
                                var text = page.GetText();

                                if (!string.IsNullOrWhiteSpace(text))
                                {
                                    Log.Debug("Texto extraído ({ImageType}-{ConfigName}): {Text}", imgType, config.Name, text.Trim());

                                    var dni = ExtractDNIFromText(text);
                                    if (!string.IsNullOrEmpty(dni))
                                    {
                                        Log.Information("DNI encontrado con imagen {ImageType} y configuración {ConfigName}: {DNI}", imgType, config.Name, dni);
                                        return dni;
                                    }
                                }
                            }
                            catch (Exception configEx)
                            {
                                Log.Warning(configEx, "Error con imagen {ImageType} y configuración {ConfigName}", imgType, config.Name);
                            }
                        }
                    }

                    Log.Warning("No se encontró DNI en la imagen con ninguna combinación de procesamiento y configuración OCR");
                    return null;
                }
                finally
                {
                    // Limpiar imágenes procesadas (excepto la original que se limpia automáticamente)
                    for (int i = 1; i < processedImages.Count; i++)
                    {
                        processedImages[i]?.Dispose();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al procesar imagen con OCR: {ImagePath}", imagePath);
                return null;
            }
        }

        private async Task<string?> ExtractDNIFromScannedPDF(string pdfPath)
        {
            try
            {
                Log.Information("Procesando PDF escaneado para OCR: {PdfPath}", pdfPath);

                // Método 1: Intentar extraer imágenes del PDF
                var extractedImages = ExtractImagesFromPDF(pdfPath);

                if (extractedImages.Count > 0)
                {
                    Log.Information("Encontradas {Count} imágenes en el PDF para procesar con OCR", extractedImages.Count);

                    // Procesar cada imagen extraída con OCR
                    foreach (var imagePath in extractedImages)
                    {
                        try
                        {
                            Log.Information("Procesando imagen extraída: {ImagePath}", imagePath);
                            var dni = await ProcessImageWithOCR(imagePath);
                            if (!string.IsNullOrEmpty(dni))
                            {
                                Log.Information("DNI encontrado en imagen extraída del PDF: {DNI}", dni);

                                // Limpiar archivos temporales
                                CleanupTempImages(extractedImages);
                                return dni;
                            }
                        }
                        catch (Exception ex)
                        {
                            Log.Warning(ex, "Error al procesar imagen extraída: {ImagePath}", imagePath);
                        }
                    }

                    // Limpiar archivos temporales
                    CleanupTempImages(extractedImages);
                }
                else
                {
                    Log.Warning("No se encontraron imágenes embebidas en el PDF");
                }

                // Método 2: Intentar convertir páginas del PDF a imágenes usando Docnet
                Log.Information("Intentando convertir páginas del PDF a imágenes para OCR");
                var pageImages = ConvertPDFPagesToImages(pdfPath);

                if (pageImages.Count > 0)
                {
                    Log.Information("Convertidas {Count} páginas del PDF a imágenes", pageImages.Count);

                    foreach (var imagePath in pageImages)
                    {
                        try
                        {
                            Log.Information("Procesando página convertida: {ImagePath}", imagePath);
                            var dni = await ProcessImageWithOCR(imagePath);
                            if (!string.IsNullOrEmpty(dni))
                            {
                                Log.Information("DNI encontrado en página convertida del PDF: {DNI}", dni);

                                // Limpiar archivos temporales
                                CleanupTempImages(pageImages);
                                return dni;
                            }
                        }
                        catch (Exception ex)
                        {
                            Log.Warning(ex, "Error al procesar página convertida: {ImagePath}", imagePath);
                        }
                    }

                    // Limpiar archivos temporales
                    CleanupTempImages(pageImages);
                }
                else
                {
                    Log.Warning("No se pudieron convertir páginas del PDF a imágenes");
                }

                // Método 3: Fallback - intentar extraer texto directamente
                Log.Information("Intentando extracción de texto directo como último recurso");
                var textResult = await ExtractDNIFromPDFAsync(pdfPath);
                if (!string.IsNullOrEmpty(textResult))
                {
                    Log.Information("DNI encontrado mediante extracción de texto directo: {DNI}", textResult);
                    return textResult;
                }

                Log.Warning("No se encontró DNI en el PDF escaneado usando ningún método");
                return null;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al extraer DNI del PDF escaneado: {PdfPath}", pdfPath);
                return null;
            }
        }

        public async Task<string?> ExtractDNIFromImageAsync(Bitmap bitmap)
        {
            try
            {
                if (_engine == null)
                {
                    Log.Error("Motor OCR no inicializado");
                    return null;
                }

                using var memoryStream = new MemoryStream();
                bitmap.Save(memoryStream, System.Drawing.Imaging.ImageFormat.Png);
                memoryStream.Position = 0;

                using var img = Pix.LoadFromMemory(memoryStream.ToArray());
                using var page = _engine.Process(img);

                var text = page.GetText();
                Log.Debug("Texto extraído por OCR del bitmap: {Text}", text);

                return ExtractDNIFromText(text);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al extraer DNI del bitmap");
                return null;
            }
        }

        private string? ExtractDNIFromText(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return null;

            var validDNIs = FindAllValidDNIs(text);

            if (validDNIs.Count == 0)
            {
                Log.Warning("No se encontró un DNI válido en el texto");
                return null;
            }

            if (validDNIs.Count == 1)
            {
                Log.Information("DNI extraído exitosamente: {DNI}", validDNIs[0]);
                return validDNIs[0];
            }

            // Si hay múltiples DNIs, mostrar diálogo de selección
            Log.Information("Múltiples DNIs encontrados: {DNIs}", string.Join(", ", validDNIs));
            return ShowDNISelectionDialog(validDNIs);
        }

        private List<string> FindAllValidDNIs(string text)
        {
            var validDNIs = new List<string>();

            // Patrones para buscar DNI (8 dígitos)
            var patterns = new[]
            {
                @"\b\d{8}\b",                           // 8 dígitos exactos
                @"DNI[:\s]*(\d{8})",                   // DNI: 12345678
                @"D\.N\.I[:\s]*(\d{8})",              // D.N.I: 12345678
                @"DOCUMENTO[:\s]*(\d{8})",             // DOCUMENTO: 12345678
                @"N[°º]\s*(\d{8})",                    // N° 12345678
                @"(\d{2})\s*\.?\s*(\d{3})\s*\.?\s*(\d{3})" // 12.345.678 o 12 345 678
            };

            foreach (var pattern in patterns)
            {
                var matches = Regex.Matches(text, pattern, RegexOptions.IgnoreCase);
                foreach (Match match in matches)
                {
                    string dni;
                    if (match.Groups.Count > 1)
                    {
                        // Si hay grupos de captura, usar el primer grupo
                        dni = match.Groups[1].Value;
                    }
                    else if (pattern.Contains(@"(\d{2})"))
                    {
                        // Para el patrón con puntos, concatenar los grupos
                        dni = match.Groups[1].Value + match.Groups[2].Value + match.Groups[3].Value;
                    }
                    else
                    {
                        dni = match.Value;
                    }

                    // Limpiar y validar
                    dni = Regex.Replace(dni, @"[^\d]", "");

                    if (dni.Length == 8 && IsValidDNI(dni) && !validDNIs.Contains(dni))
                    {
                        validDNIs.Add(dni);
                    }
                }
            }

            return validDNIs;
        }

        private string? ShowDNISelectionDialog(List<string> dnis)
        {
            try
            {
                // Crear formulario de selección
                using (var form = new Form())
                {
                    form.Text = "Múltiples DNIs Encontrados";
                    form.Size = new Size(400, 300);
                    form.StartPosition = FormStartPosition.CenterParent;
                    form.FormBorderStyle = FormBorderStyle.FixedDialog;
                    form.MaximizeBox = false;
                    form.MinimizeBox = false;

                    var label = new Label
                    {
                        Text = "Se encontraron múltiples DNIs. Seleccione el correcto:",
                        Location = new Point(10, 10),
                        Size = new Size(360, 40),
                        AutoSize = false
                    };

                    var listBox = new ListBox
                    {
                        Location = new Point(10, 60),
                        Size = new Size(360, 150),
                        SelectionMode = SelectionMode.One
                    };

                    foreach (var dni in dnis)
                    {
                        listBox.Items.Add(dni);
                    }
                    listBox.SelectedIndex = 0; // Seleccionar el primero por defecto

                    var btnOK = new Button
                    {
                        Text = "Aceptar",
                        Location = new Point(210, 220),
                        Size = new Size(80, 30),
                        DialogResult = DialogResult.OK
                    };

                    var btnCancel = new Button
                    {
                        Text = "Cancelar",
                        Location = new Point(300, 220),
                        Size = new Size(80, 30),
                        DialogResult = DialogResult.Cancel
                    };

                    form.Controls.AddRange(new Control[] { label, listBox, btnOK, btnCancel });
                    form.AcceptButton = btnOK;
                    form.CancelButton = btnCancel;

                    if (form.ShowDialog() == DialogResult.OK && listBox.SelectedItem != null)
                    {
                        return listBox.SelectedItem.ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al mostrar diálogo de selección de DNI");
            }

            // Si hay error o cancelación, devolver el primer DNI
            return dnis.FirstOrDefault();
        }

        private bool IsValidDNI(string dni)
        {
            // Validaciones básicas para DNI argentino
            if (string.IsNullOrWhiteSpace(dni) || dni.Length != 8)
                return false;

            // No debe ser todo el mismo número
            if (dni.All(c => c == dni[0]))
                return false;

            // Debe ser numérico
            return dni.All(char.IsDigit);
        }

        public Bitmap PreprocessImage(Bitmap original)
        {
            try
            {
                // Crear una copia para procesar
                var processed = new Bitmap(original.Width, original.Height);

                using (var g = Graphics.FromImage(processed))
                {
                    // Mejorar contraste y convertir a escala de grises
                    var colorMatrix = new System.Drawing.Imaging.ColorMatrix(new float[][]
                    {
                        new float[] {0.299f, 0.299f, 0.299f, 0, 0},
                        new float[] {0.587f, 0.587f, 0.587f, 0, 0},
                        new float[] {0.114f, 0.114f, 0.114f, 0, 0},
                        new float[] {0, 0, 0, 1, 0},
                        new float[] {0, 0, 0, 0, 1}
                    });

                    var attributes = new System.Drawing.Imaging.ImageAttributes();
                    attributes.SetColorMatrix(colorMatrix);

                    g.DrawImage(original, new Rectangle(0, 0, original.Width, original.Height),
                        0, 0, original.Width, original.Height, GraphicsUnit.Pixel, attributes);
                }

                return processed;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al preprocesar la imagen");
                return original;
            }
        }

        public async Task<string?> ExtractDNIFromPDFAsync(string pdfPath)
        {
            try
            {
                Log.Information("Iniciando extracción de DNI desde PDF: {PdfPath}", pdfPath);

                // Primero intentar extraer texto directamente del PDF
                var pdfText = ExtractTextFromPDF(pdfPath);
                if (!string.IsNullOrEmpty(pdfText))
                {
                    var dni = ExtractDNIFromText(pdfText);
                    if (!string.IsNullOrEmpty(dni))
                    {
                        Log.Information("DNI extraído del texto del PDF: {DNI}", dni);
                        return dni;
                    }
                }

                Log.Information("No se encontró DNI en el texto del PDF, iniciando OCR completo en imágenes");

                // Usar el método mejorado de OCR para PDFs escaneados
                return await ExtractDNIFromScannedPDF(pdfPath);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al extraer DNI del PDF: {PdfPath}", pdfPath);
                return null;
            }
        }

        private string ExtractTextFromPDF(string pdfPath)
        {
            try
            {
                var text = new StringBuilder();

                using (var reader = new PdfReader(pdfPath))
                {
                    // Verificar que el PDF no esté protegido
                    if (reader.IsEncrypted())
                    {
                        Log.Warning("El PDF está protegido con contraseña: {PdfPath}", pdfPath);
                        return string.Empty;
                    }

                    // Extraer texto de las primeras 3 páginas (donde usualmente está el DNI)
                    int pagesToProcess = Math.Min(reader.NumberOfPages, 3);

                    for (int page = 1; page <= pagesToProcess; page++)
                    {
                        try
                        {
                            var pageText = PdfTextExtractor.GetTextFromPage(reader, page);
                            if (!string.IsNullOrWhiteSpace(pageText))
                            {
                                text.AppendLine(pageText);
                            }
                        }
                        catch (Exception pageEx)
                        {
                            Log.Warning(pageEx, "Error al extraer texto de la página {Page} del PDF", page);
                        }
                    }
                }

                // Log.Debug("Texto extraído del PDF: {Text}", text.ToString());
                return text.ToString();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al extraer texto del PDF");
                return string.Empty;
            }
        }

        private List<string> ExtractImagesFromPDF(string pdfPath)
        {
            var extractedImages = new List<string>();

            try
            {
                using (var reader = new PdfReader(pdfPath))
                {
                    for (int pageNum = 1; pageNum <= reader.NumberOfPages; pageNum++)
                    {
                        var page = reader.GetPageN(pageNum);
                        var resources = page.GetAsDict(PdfName.RESOURCES);

                        if (resources != null)
                        {
                            var xObjects = resources.GetAsDict(PdfName.XOBJECT);
                            if (xObjects != null)
                            {
                                foreach (var name in xObjects.Keys)
                                {
                                    var obj = xObjects.GetDirectObject(name);
                                    if (obj.IsStream())
                                    {
                                        var stream = (PRStream)obj;
                                        var subtype = stream.GetAsName(PdfName.SUBTYPE);

                                        if (PdfName.IMAGE.Equals(subtype))
                                        {
                                            try
                                            {
                                                var imageBytes = PdfReader.GetStreamBytes(stream);
                                                var tempImagePath = System.IO.Path.Combine(System.IO.Path.GetTempPath(),
                                                    $"pdf_image_{pageNum}_{name}_{Guid.NewGuid()}.png");

                                                // Intentar crear imagen desde los bytes
                                                using (var ms = new MemoryStream(imageBytes))
                                                {
                                                    try
                                                    {
                                                        using (var image = System.Drawing.Image.FromStream(ms))
                                                        {
                                                            image.Save(tempImagePath, System.Drawing.Imaging.ImageFormat.Png);
                                                            extractedImages.Add(tempImagePath);
                                                            Log.Information("Imagen extraída del PDF: {ImagePath}", tempImagePath);
                                                        }
                                                    }
                                                    catch
                                                    {
                                                        // Si falla la conversión directa, intentar como bitmap
                                                        File.WriteAllBytes(tempImagePath, imageBytes);
                                                        extractedImages.Add(tempImagePath);
                                                        Log.Information("Imagen extraída como bytes del PDF: {ImagePath}", tempImagePath);
                                                    }
                                                }
                                            }
                                            catch (Exception ex)
                                            {
                                                Log.Warning(ex, "Error al extraer imagen de la página {PageNum}", pageNum);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                Log.Information("Extraídas {Count} imágenes del PDF", extractedImages.Count);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al extraer imágenes del PDF: {PdfPath}", pdfPath);
            }

            return extractedImages;
        }

        private List<string> ConvertPDFPagesToImages(string pdfPath)
        {
            var pageImages = new List<string>();

            try
            {
                using (var docReader = Docnet.Core.DocLib.Instance.GetDocReader(pdfPath, new Docnet.Core.Models.PageDimensions(1200, 1600)))
                {
                    var pageCount = docReader.GetPageCount();
                    Log.Information("PDF tiene {PageCount} páginas para convertir", pageCount);

                    // Procesar máximo las primeras 3 páginas (donde usualmente está el DNI)
                    var pagesToProcess = Math.Min(pageCount, 3);

                    for (int pageIndex = 0; pageIndex < pagesToProcess; pageIndex++)
                    {
                        try
                        {
                            using (var pageReader = docReader.GetPageReader(pageIndex))
                            {
                                var rawBytes = pageReader.GetImage();
                                var width = pageReader.GetPageWidth();
                                var height = pageReader.GetPageHeight();

                                // Crear bitmap desde los bytes raw
                                using (var bitmap = new Bitmap(width, height, System.Drawing.Imaging.PixelFormat.Format32bppArgb))
                                {
                                    var bitmapData = bitmap.LockBits(new Rectangle(0, 0, width, height),
                                        System.Drawing.Imaging.ImageLockMode.WriteOnly,
                                        System.Drawing.Imaging.PixelFormat.Format32bppArgb);

                                    System.Runtime.InteropServices.Marshal.Copy(rawBytes, 0, bitmapData.Scan0, rawBytes.Length);
                                    bitmap.UnlockBits(bitmapData);

                                    // Guardar como archivo temporal
                                    var tempImagePath = System.IO.Path.Combine(System.IO.Path.GetTempPath(),
                                        $"pdf_page_{pageIndex + 1}_{Guid.NewGuid()}.png");

                                    bitmap.Save(tempImagePath, System.Drawing.Imaging.ImageFormat.Png);
                                    pageImages.Add(tempImagePath);

                                    Log.Information("Página {PageNum} convertida a imagen: {ImagePath}", pageIndex + 1, tempImagePath);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Log.Warning(ex, "Error al convertir página {PageIndex} del PDF", pageIndex + 1);
                        }
                    }
                }

                Log.Information("Convertidas {Count} páginas del PDF a imágenes", pageImages.Count);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al convertir páginas del PDF: {PdfPath}", pdfPath);
            }

            return pageImages;
        }

        private void CleanupTempImages(List<string> imagePaths)
        {
            foreach (var imagePath in imagePaths)
            {
                try
                {
                    if (File.Exists(imagePath))
                    {
                        File.Delete(imagePath);
                        Log.Debug("Archivo temporal eliminado: {ImagePath}", imagePath);
                    }
                }
                catch (Exception ex)
                {
                    Log.Warning(ex, "No se pudo eliminar archivo temporal: {ImagePath}", imagePath);
                }
            }
        }

        private async Task<bool> DownloadTessDataFileAsync(string language, string destinationPath)
        {
            try
            {
                Log.Information("Intentando descargar archivo de idioma OCR: {Language}", language);

                var url = $"https://github.com/tesseract-ocr/tessdata/raw/refs/heads/main/{language}.traineddata";

                using (var client = new HttpClient())
                {
                    // Configurar timeout y headers
                    client.Timeout = TimeSpan.FromMinutes(10);
                    client.DefaultRequestHeaders.Add("User-Agent", "SM_Gestion_Documentos/1.0");

                    Log.Information("Descargando desde: {Url}", url);

                    // Crear directorio si no existe
                    var directory = System.IO.Path.GetDirectoryName(destinationPath);
                    if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }

                    // Descargar con progreso
                    using (var response = await client.GetAsync(url, HttpCompletionOption.ResponseHeadersRead))
                    {
                        if (response.IsSuccessStatusCode)
                        {
                            var totalBytes = response.Content.Headers.ContentLength ?? 0;
                            Log.Information("Iniciando descarga de {TotalMB:F2} MB...", totalBytes / (1024.0 * 1024.0));

                            using (var contentStream = await response.Content.ReadAsStreamAsync())
                            using (var fileStream = new FileStream(destinationPath, FileMode.Create, FileAccess.Write, FileShare.None, 8192, true))
                            {
                                var buffer = new byte[8192];
                                var totalBytesRead = 0L;
                                int bytesRead;

                                while ((bytesRead = await contentStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                                {
                                    await fileStream.WriteAsync(buffer, 0, bytesRead);
                                    totalBytesRead += bytesRead;

                                    // Log progreso cada 1MB
                                    if (totalBytesRead % (1024 * 1024) == 0 || totalBytesRead == totalBytes)
                                    {
                                        var progressMB = totalBytesRead / (1024.0 * 1024.0);
                                        var totalMB = totalBytes / (1024.0 * 1024.0);
                                        var percentage = totalBytes > 0 ? (totalBytesRead * 100.0 / totalBytes) : 0;
                                        Log.Information("Descarga en progreso: {ProgressMB:F1}/{TotalMB:F1} MB ({Percentage:F1}%)",
                                            progressMB, totalMB, percentage);
                                    }
                                }
                            }

                            var finalSizeMB = new FileInfo(destinationPath).Length / (1024.0 * 1024.0);
                            Log.Information("Archivo descargado exitosamente: {DestinationPath} ({FileSizeMB:F2} MB)",
                                destinationPath, finalSizeMB);

                            return true;
                        }
                        else
                        {
                            Log.Error("Error al descargar archivo: HTTP {StatusCode} - {ReasonPhrase}",
                                response.StatusCode, response.ReasonPhrase);
                            return false;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al descargar archivo de idioma OCR: {Language}", language);

                // Limpiar archivo parcial si existe
                try
                {
                    if (File.Exists(destinationPath))
                    {
                        File.Delete(destinationPath);
                        Log.Information("Archivo parcial eliminado: {DestinationPath}", destinationPath);
                    }
                }
                catch (Exception deleteEx)
                {
                    Log.Warning(deleteEx, "No se pudo eliminar archivo parcial: {DestinationPath}", destinationPath);
                }

                return false;
            }
        }

        public void Dispose()
        {
            _engine?.Dispose();
        }
    }
}
