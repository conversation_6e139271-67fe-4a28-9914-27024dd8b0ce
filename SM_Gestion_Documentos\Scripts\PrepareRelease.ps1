# Script para preparar la distribución Release
# Ejecutar desde la carpeta del proyecto

param(
    [string]$Configuration = "Release",
    [string]$OutputDir = "Distribution"
)

Write-Host "Preparando distribución $Configuration..." -ForegroundColor Green

# Rutas
$ProjectPath = Get-Location
$ReleasePath = "bin\$Configuration\net8.0-windows"
$DistributionPath = Join-Path $ProjectPath $OutputDir

# Verificar que existe la compilación
if (-not (Test-Path $ReleasePath)) {
    Write-Host "Error: No se encontró la compilación en: $ReleasePath" -ForegroundColor Red
    Write-Host "Ejecute primero: dotnet build --configuration $Configuration" -ForegroundColor Yellow
    exit 1
}

# Crear directorio de distribución
if (Test-Path $DistributionPath) {
    Remove-Item $DistributionPath -Recurse -Force
}
New-Item -ItemType Directory -Path $DistributionPath -Force | Out-Null

Write-Host "Copiando archivos principales..." -ForegroundColor Cyan

# Copiar archivos principales
Copy-Item "$ReleasePath\*" $DistributionPath -Recurse -Force

# Limpiar archivos innecesarios
Write-Host "Limpiando archivos innecesarios..." -ForegroundColor Cyan

# Carpetas de localización a eliminar
$LocaleFolders = @(
    "cs", "de", "fr", "it", "ja", "ko", "pl", "pt-BR", "ru", "tr", "zh-Hans", "zh-Hant"
)

foreach ($locale in $LocaleFolders) {
    $localePath = Join-Path $DistributionPath $locale
    if (Test-Path $localePath) {
        Remove-Item $localePath -Recurse -Force
        Write-Host "  Eliminado: $locale" -ForegroundColor Yellow
    }
}

# Eliminar archivos de desarrollo
$DevFiles = @(
    "*.pdb",
    "*.xml",
    "*.dev.json"
)

foreach ($pattern in $DevFiles) {
    $files = Get-ChildItem -Path $DistributionPath -Filter $pattern -Recurse
    foreach ($file in $files) {
        Remove-Item $file.FullName -Force
        Write-Host "  Eliminado: $($file.Name)" -ForegroundColor Yellow
    }
}

# Limpiar runtimes innecesarios
$runtimesPath = Join-Path $DistributionPath "runtimes"
if (Test-Path $runtimesPath) {
    $runtimeFolders = Get-ChildItem $runtimesPath -Directory
    foreach ($folder in $runtimeFolders) {
        if ($folder.Name -notmatch "^win-(x64|x86)$") {
            Remove-Item $folder.FullName -Recurse -Force
            Write-Host "  Eliminado runtime: $($folder.Name)" -ForegroundColor Yellow
        }
    }
}

# Verificar archivos críticos
Write-Host "Verificando archivos críticos..." -ForegroundColor Cyan

$CriticalFiles = @(
    "SM_Gestion_Documentos.exe",
    "SM_Gestion_Documentos.dll",
    "App.config"
)

$missingFiles = @()
foreach ($file in $CriticalFiles) {
    $filePath = Join-Path $DistributionPath $file
    if (-not (Test-Path $filePath)) {
        $missingFiles += $file
    } else {
        Write-Host "  ✓ $file" -ForegroundColor Green
    }
}

if ($missingFiles.Count -gt 0) {
    Write-Host "Error: Archivos críticos faltantes:" -ForegroundColor Red
    foreach ($file in $missingFiles) {
        Write-Host "  ✗ $file" -ForegroundColor Red
    }
    exit 1
}

# Verificar/crear directorio tessdata
$tessDataPath = Join-Path $DistributionPath "tessdata"
if (-not (Test-Path $tessDataPath)) {
    New-Item -ItemType Directory -Path $tessDataPath -Force | Out-Null
    Write-Host "  ✓ Directorio tessdata creado" -ForegroundColor Green
} else {
    Write-Host "  ✓ Directorio tessdata existe" -ForegroundColor Green
}

# Verificar archivo spa.traineddata
$spaFile = Join-Path $tessDataPath "spa.traineddata"
if (Test-Path $spaFile) {
    $spaSize = (Get-Item $spaFile).Length / 1MB
    Write-Host "  ✓ spa.traineddata existe ($([math]::Round($spaSize, 2)) MB)" -ForegroundColor Green
} else {
    Write-Host "  ⚠ spa.traineddata no encontrado - se descargará automáticamente en la primera ejecución" -ForegroundColor Yellow
}

# Mostrar estadísticas finales
$totalSize = (Get-ChildItem $DistributionPath -Recurse | Measure-Object -Property Length -Sum).Sum
$sizeInMB = [math]::Round($totalSize / 1MB, 2)
$fileCount = (Get-ChildItem $DistributionPath -Recurse -File | Measure-Object).Count

Write-Host "`n=== DISTRIBUCIÓN COMPLETADA ===" -ForegroundColor Green
Write-Host "Ubicación: $DistributionPath" -ForegroundColor Cyan
Write-Host "Tamaño total: $sizeInMB MB" -ForegroundColor Cyan
Write-Host "Archivos: $fileCount" -ForegroundColor Cyan

# Listar archivos principales
Write-Host "`nArchivos principales:" -ForegroundColor Cyan
Get-ChildItem $DistributionPath -File | Where-Object { $_.Extension -in @('.exe', '.dll', '.config') } | 
    Sort-Object Name | ForEach-Object {
    $size = [math]::Round($_.Length / 1KB, 1)
    Write-Host "  $($_.Name) ($size KB)" -ForegroundColor White
}

Write-Host "`n¡Distribución lista para entrega!" -ForegroundColor Green
Write-Host "Puede comprimir la carpeta '$OutputDir' para distribución." -ForegroundColor Yellow
