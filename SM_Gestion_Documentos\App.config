﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<appSettings>
		<!-- Configuración de Base de Datos -->
		<add key="DatabasePath" value="DocumentManager.db" />

		<!-- Configuración de Almacenamiento -->
		<add key="StorageType" value="Local" />
		<add key="LocalStoragePath" value="SM_Gestion_Documentos" />
		<add key="NetworkStoragePath" value="\\server\shared\documents" />
		<add key="MaxFileSizeMB" value="50" />
		<add key="AllowedFileExtensions" value=".pdf,.jpg,.jpeg,.png,.tiff,.bmp" />

		<!-- Configuración de OCR -->
		<add key="TessDataPath" value="tessdata" />
		<add key="OCRLanguage" value="spa" />
		<add key="OCRTimeout" value="30" />
		<add key="OCRDPIResolution" value="300" />

		<!-- Configuración de Escaneo -->
		<add key="ScannerTimeout" value="60" />
		<add key="DefaultScanFormat" value="PDF" />
		<add key="ScanResolution" value="300" />
		<add key="ScanColorMode" value="Color" />

		<!-- Configuración de Active Directory -->
		<add key="UseActiveDirectory" value="false" />
		<add key="ActiveDirectoryDomain" value="your-domain.com" />
		<add key="ActiveDirectoryServer" value="ldap://your-domain.com" />

		<!-- Configuración de Auditoría -->
		<add key="AuditLogRetentionDays" value="365" />
		<add key="EnableDetailedLogging" value="true" />

		<!-- Configuración de Logs -->
		<add key="LogFilePath" value="Logs\app-.log" />
		<add key="LogLevel" value="Information" />

		<!-- Configuración de Nube -->
		<add key="GoogleDriveEnabled" value="false" />
		<add key="OneDriveEnabled" value="false" />
		<add key="SharePointEnabled" value="false" />
	</appSettings>

	<connectionStrings>
		<add name="DefaultConnection"
			 connectionString="Data Source=DocumentManager.db"
			 providerName="Microsoft.Data.Sqlite" />
	</connectionStrings>

	<startup>
		<supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
	</startup>
</configuration>