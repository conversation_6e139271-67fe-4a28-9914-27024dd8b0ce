using System;
using System.Configuration;
using System.IO;
using Serilog;

namespace SM_Gestion_Documentos.Configuration
{
    public static class AppConfig
    {
        // Configuración de Base de Datos
        public static string DatabasePath => GetConfigValue("DatabasePath", "DocumentManager.db");
        
        // Configuración de Almacenamiento
        public static string StorageType => GetConfigValue("StorageType", "Local");
        public static string LocalStoragePath => GetFullStoragePath();
        public static string NetworkStoragePath => GetConfigValue("NetworkStoragePath", @"\\server\shared\documents");
        public static int MaxFileSizeMB => GetConfigValueInt("MaxFileSizeMB", 50);
        public static string[] AllowedFileExtensions => GetConfigValue("AllowedFileExtensions", ".pdf,.jpg,.jpeg,.png,.tiff,.bmp").Split(',');
        
        // Configuración de OCR
        public static string TessDataPath => GetConfigValue("TessDataPath", "tessdata");
        public static string OCRLanguage => GetConfigValue("OCRLanguage", "spa");
        public static int OCRTimeout => GetConfigValueInt("OCRTimeout", 30);
        public static int OCRDPIResolution => GetConfigValueInt("OCRDPIResolution", 300);
        
        // Configuración de Escaneo
        public static int ScannerTimeout => GetConfigValueInt("ScannerTimeout", 60);
        public static string DefaultScanFormat => GetConfigValue("DefaultScanFormat", "PDF");
        public static int ScanResolution => GetConfigValueInt("ScanResolution", 300);
        public static string ScanColorMode => GetConfigValue("ScanColorMode", "Color");
        
        // Configuración de Active Directory
        public static bool UseActiveDirectory => GetConfigValueBool("UseActiveDirectory", false);
        public static string ActiveDirectoryDomain => GetConfigValue("ActiveDirectoryDomain", "your-domain.com");
        public static string ActiveDirectoryServer => GetConfigValue("ActiveDirectoryServer", "ldap://your-domain.com");
        
        // Configuración de Auditoría
        public static int AuditLogRetentionDays => GetConfigValueInt("AuditLogRetentionDays", 365);
        public static bool EnableDetailedLogging => GetConfigValueBool("EnableDetailedLogging", true);
        
        // Configuración de Logs
        public static string LogFilePath => GetConfigValue("LogFilePath", @"Logs\app-.log");
        public static string LogLevel => GetConfigValue("LogLevel", "Information");
        
        // Configuración de Nube
        public static bool GoogleDriveEnabled => GetConfigValueBool("GoogleDriveEnabled", false);
        public static bool OneDriveEnabled => GetConfigValueBool("OneDriveEnabled", false);
        public static bool SharePointEnabled => GetConfigValueBool("SharePointEnabled", false);
        
        // Métodos auxiliares
        private static string GetConfigValue(string key, string defaultValue)
        {
            try
            {
                var value = ConfigurationManager.AppSettings[key];
                return string.IsNullOrEmpty(value) ? defaultValue : value;
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "Error al leer configuración {Key}, usando valor por defecto: {DefaultValue}", key, defaultValue);
                return defaultValue;
            }
        }
        
        private static int GetConfigValueInt(string key, int defaultValue)
        {
            try
            {
                var value = GetConfigValue(key, defaultValue.ToString());
                return int.TryParse(value, out var result) ? result : defaultValue;
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "Error al leer configuración numérica {Key}, usando valor por defecto: {DefaultValue}", key, defaultValue);
                return defaultValue;
            }
        }
        
        private static bool GetConfigValueBool(string key, bool defaultValue)
        {
            try
            {
                var value = GetConfigValue(key, defaultValue.ToString());
                return bool.TryParse(value, out var result) ? result : defaultValue;
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "Error al leer configuración booleana {Key}, usando valor por defecto: {DefaultValue}", key, defaultValue);
                return defaultValue;
            }
        }
        
        private static string GetFullStoragePath()
        {
            try
            {
                var configPath = GetConfigValue("LocalStoragePath", @"Documents\SM_Gestion_Documentos");
                
                // Si es una ruta relativa, usar la carpeta Documents del usuario
                if (!Path.IsPathRooted(configPath))
                {
                    var documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
                    var fullPath = Path.Combine(documentsPath, configPath);
                    
                    // Crear directorio si no existe
                    Directory.CreateDirectory(fullPath);
                    
                    Log.Information("Ruta de almacenamiento configurada: {StoragePath}", fullPath);
                    return fullPath;
                }
                
                // Si es una ruta absoluta, usarla directamente
                Directory.CreateDirectory(configPath);
                return configPath;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al configurar ruta de almacenamiento, usando ruta por defecto");
                var defaultPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "SM_Gestion_Documentos");
                Directory.CreateDirectory(defaultPath);
                return defaultPath;
            }
        }
        
        // Método para validar configuración al inicio
        public static void ValidateConfiguration()
        {
            try
            {
                Log.Information("Validando configuración de la aplicación...");
                
                // Validar ruta de almacenamiento
                var storagePath = LocalStoragePath;
                Log.Information("Ruta de almacenamiento: {StoragePath}", storagePath);
                
                // Validar ruta de tessdata
                var tessDataPath = TessDataPath;
                if (!Directory.Exists(tessDataPath))
                {
                    Log.Warning("Directorio tessdata no encontrado: {TessDataPath}", tessDataPath);
                }
                else
                {
                    Log.Information("Directorio tessdata encontrado: {TessDataPath}", tessDataPath);
                }
                
                // Validar configuración de base de datos
                Log.Information("Base de datos configurada: {DatabasePath}", DatabasePath);
                
                // Mostrar configuraciones principales
                Log.Information("Configuración cargada - Tipo almacenamiento: {StorageType}, OCR: {OCRLanguage}, Logs: {LogLevel}", 
                    StorageType, OCRLanguage, LogLevel);
                
                Log.Information("Configuración validada exitosamente");
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error al validar configuración");
                throw;
            }
        }
    }
}
