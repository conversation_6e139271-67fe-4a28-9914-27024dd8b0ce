﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
	<OutputType>WinExe</OutputType>
	<TargetFramework>net8.0-windows</TargetFramework>
	<Nullable>enable</Nullable>
	<UseWindowsForms>true</UseWindowsForms>
	<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Docnet.Core" Version="2.6.0" />
		<PackageReference Include="iTextSharp" Version="5.5.13.3" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0" />
		<PackageReference Include="System.Management" Version="8.0.0" />
		<PackageReference Include="Tesseract" Version="5.2.0" />
		<PackageReference Include="Serilog" Version="3.1.1" />
		<PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
		<PackageReference Include="System.DirectoryServices" Version="8.0.0" />
		<PackageReference Include="Google.Apis.Drive.v3" Version="1.68.0.3411" />
		<PackageReference Include="Microsoft.Graph" Version="5.42.0" />
		<PackageReference Include="System.Drawing.Common" Version="8.0.0" />
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="8.0.0" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Forms\assets\" />
	</ItemGroup>

  <ItemGroup>
    <Content Include="Forms\assets\321111965_458441543158373_8906602713859774882_n.jpg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="App.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="spa.traineddata" Condition="Exists('spa.traineddata')">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>tessdata\spa.traineddata</TargetPath>
    </Content>
  </ItemGroup>

  <!-- Configuración para compilación Release optimizada -->
  <PropertyGroup Condition="'$(Configuration)'=='Release'">
    <PublishSingleFile>false</PublishSingleFile>
    <SelfContained>false</SelfContained>
    <PublishReadyToRun>true</PublishReadyToRun>
    <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
    <DebugType>none</DebugType>
    <DebugSymbols>false</DebugSymbols>
  </PropertyGroup>

  <!-- Excluir carpetas de localización innecesarias en Release -->
  <ItemGroup Condition="'$(Configuration)'=='Release'">
    <Content Remove="bin\Release\**\runtimes\**\lib\**\*.resources.dll" />
    <None Remove="bin\Release\**\cs\**" />
    <None Remove="bin\Release\**\de\**" />
    <None Remove="bin\Release\**\fr\**" />
    <None Remove="bin\Release\**\it\**" />
    <None Remove="bin\Release\**\ja\**" />
    <None Remove="bin\Release\**\ko\**" />
    <None Remove="bin\Release\**\pl\**" />
    <None Remove="bin\Release\**\pt-BR\**" />
    <None Remove="bin\Release\**\ru\**" />
    <None Remove="bin\Release\**\tr\**" />
    <None Remove="bin\Release\**\zh-Hans\**" />
    <None Remove="bin\Release\**\zh-Hant\**" />
  </ItemGroup>

  <!-- Target para copiar tessdata después de la compilación -->
  <Target Name="CopyTessData" AfterTargets="Build">
    <PropertyGroup>
      <TessDataSourceFile>spa.traineddata</TessDataSourceFile>
      <TessDataTargetDir>$(OutputPath)tessdata</TessDataTargetDir>
      <TessDataTargetFile>$(TessDataTargetDir)\spa.traineddata</TessDataTargetFile>
    </PropertyGroup>

    <!-- Crear directorio tessdata si no existe -->
    <MakeDir Directories="$(TessDataTargetDir)" Condition="!Exists('$(TessDataTargetDir)')" />

    <!-- Copiar archivo si existe en el proyecto -->
    <Copy SourceFiles="$(TessDataSourceFile)"
          DestinationFiles="$(TessDataTargetFile)"
          Condition="Exists('$(TessDataSourceFile)') And !Exists('$(TessDataTargetFile)')" />

    <!-- Mensaje informativo -->
    <Message Text="Directorio tessdata creado: $(TessDataTargetDir)"
             Importance="normal"
             Condition="!Exists('$(TessDataTargetDir)')" />
    <Message Text="Archivo tessdata copiado: $(TessDataTargetFile)"
             Importance="normal"
             Condition="Exists('$(TessDataSourceFile)') And !Exists('$(TessDataTargetFile)')" />
  </Target>

</Project>