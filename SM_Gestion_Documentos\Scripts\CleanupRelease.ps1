# Script para limpiar la compilación Release de archivos innecesarios
# Ejecutar desde la carpeta del proyecto

param(
    [string]$ReleasePath = "bin\Release\net8.0-windows"
)

Write-Host "Limpiando compilación Release..." -ForegroundColor Green

if (-not (Test-Path $ReleasePath)) {
    Write-Host "Error: No se encontró la carpeta Release en: $ReleasePath" -ForegroundColor Red
    exit 1
}

# Carpetas de localización a eliminar
$LocaleFolders = @(
    "cs", "de", "fr", "it", "ja", "ko", "pl", "pt-BR", "ru", "tr", "zh-Hans", "zh-Hant"
)

# Eliminar carpetas de localización
foreach ($locale in $LocaleFolders) {
    $localePath = Join-Path $ReleasePath $locale
    if (Test-Path $localePath) {
        Remove-Item $localePath -Recurse -Force
        Write-Host "Eliminado: $locale" -ForegroundColor Yellow
    }
}

# Eliminar archivos de recursos innecesarios
$ResourcePatterns = @(
    "*.resources.dll",
    "*.pdb",
    "*.xml" # Documentación XML
)

foreach ($pattern in $ResourcePatterns) {
    $files = Get-ChildItem -Path $ReleasePath -Filter $pattern -Recurse
    foreach ($file in $files) {
        Remove-Item $file.FullName -Force
        Write-Host "Eliminado: $($file.Name)" -ForegroundColor Yellow
    }
}

# Eliminar carpetas runtimes innecesarias (mantener solo win-x64 y win-x86)
$runtimesPath = Join-Path $ReleasePath "runtimes"
if (Test-Path $runtimesPath) {
    $runtimeFolders = Get-ChildItem $runtimesPath -Directory
    foreach ($folder in $runtimeFolders) {
        if ($folder.Name -notmatch "^win-(x64|x86)$") {
            Remove-Item $folder.FullName -Recurse -Force
            Write-Host "Eliminado runtime: $($folder.Name)" -ForegroundColor Yellow
        }
    }
}

# Mostrar tamaño final
$totalSize = (Get-ChildItem $ReleasePath -Recurse | Measure-Object -Property Length -Sum).Sum
$sizeInMB = [math]::Round($totalSize / 1MB, 2)

Write-Host "`nLimpieza completada!" -ForegroundColor Green
Write-Host "Tamaño final: $sizeInMB MB" -ForegroundColor Cyan
Write-Host "Ubicación: $ReleasePath" -ForegroundColor Cyan

# Listar archivos principales
Write-Host "`nArchivos principales:" -ForegroundColor Cyan
Get-ChildItem $ReleasePath -File | Where-Object { $_.Extension -in @('.exe', '.dll', '.config') } | 
    Sort-Object Name | ForEach-Object {
    $size = [math]::Round($_.Length / 1KB, 1)
    Write-Host "  $($_.Name) ($size KB)" -ForegroundColor White
}
