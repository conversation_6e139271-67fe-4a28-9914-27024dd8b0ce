# This script replicates the build process defined in the .github/workflows/dotnet-desktop.yml file for local execution.
# Before running, ensure you have the .NET 8.0 SDK and MSBuild installed and configured in your environment.

# Suppress warning messages for a quieter output
$WarningPreference = 'SilentlyContinue'

# --- Configuration ---
# You can optionally provide your own Base64 encoded PFX and password.
# If you leave these as placeholders, the script will generate a temporary self-signed certificate.
# To generate the Base64 string for your PFX file, use the following PowerShell command:
# [System.Convert]::ToBase64String([System.IO.File]::ReadAllBytes("C:\path\to\your\certificate.pfx"))
$base64EncodedPfx = "YOUR_BASE64_ENCODED_PFX"
$pfxPassword = "YOUR_PFX_PASSWORD"

# --- Script Variables ---
$Solution_Name = "SM_Gestion_Documentos.sln"
$Project_Directory = "SM_Gestion_Documentos"
$Project_Path = "SM_Gestion_Documentos\SM_Gestion_Documentos.csproj"
$Configuration = "Release"
$Certificate_File = Join-Path -Path $Project_Directory -ChildPath "LocalBuild.pfx"
$generatedCert = $false
$certStorePath = "Cert:\CurrentUser\My"
$cert = $null

# --- Build Steps ---

# 1. Restore the application
Write-Host "Restoring application..."
msbuild $Solution_Name /t:Restore /p:Configuration=$Configuration /v:q
if ($LASTEXITCODE -ne 0) {
    Write-Error "MSBuild restore failed."
    exit 1
}

# 2. Prepare the PFX certificate
if ($base64EncodedPfx -eq "YOUR_BASE64_ENCODED_PFX" -or -not $base64EncodedPfx) {
    Write-Host "No PFX certificate provided. Generating a new self-signed certificate for local build..."
    $generatedCert = $true
    
    # Generate a secure password for the new PFX
    $pfxPassword = [System.Guid]::NewGuid().ToString()
    Write-Host "Generated PFX Password (for this build only): $pfxPassword"

    # Create the self-signed certificate
    $certSubject = "CN=SM_Gestion_Documentos_LocalBuild"
    $cert = New-SelfSignedCertificate -Subject $certSubject -CertStoreLocation $certStorePath -KeyAlgorithm RSA -KeyLength 2048 -NotAfter (Get-Date).AddYears(1)
    
    # Export the certificate to a PFX file
    $pfxPasswordSecure = ConvertTo-SecureString -String $pfxPassword -Force -AsPlainText
    try {
        Export-PfxCertificate -Cert $cert -FilePath $Certificate_File -Password $pfxPasswordSecure -ErrorAction Stop | Out-Null
        Write-Host "Self-signed certificate '$Certificate_File' created successfully."
    }
    catch {
        Write-Error "Failed to export the self-signed certificate. The error was: $_"
        # Clean up the generated certificate from the store
        Remove-Item -Path (Join-Path $certStorePath $cert.Thumbprint) -DeleteKey -ErrorAction SilentlyContinue
        exit 1
    }
}
else {
    Write-Host "Decoding provided PFX certificate..."
    try {
        $pfx_cert_byte = [System.Convert]::FromBase64String($base64EncodedPfx)
        [System.IO.File]::WriteAllBytes($Certificate_File, $pfx_cert_byte)
        Write-Host "Certificate decoded successfully."
    }
    catch {
        Write-Error "Failed to decode Base64 PFX string. Please provide a valid string or leave it as the placeholder to generate a new one."
        exit 1
    }
}

# 3. Create the app package
Write-Host "Creating the app package..."
msbuild $Project_Path /p:Configuration=$Configuration /p:UapAppxPackageBuildMode=StoreUpload /p:AppxBundle=Always /p:AppxBundlePlatforms="x86|x64" /p:PackageCertificateKeyFile=$Certificate_File /p:PackageCertificatePassword=$pfxPassword /p:AppxPackageDir="..\AppPackages" /v:q
if ($LASTEXITCODE -ne 0) {
    Write-Error "MSBuild package creation failed."
}

# 4. Cleanup
Write-Host "Cleaning up temporary files..."
Remove-Item -Path $Certificate_File -ErrorAction SilentlyContinue

if ($generatedCert -and $cert -ne $null) {
    Write-Host "Removing generated self-signed certificate from the certificate store..."
    Remove-Item -Path (Join-Path $certStorePath $cert.Thumbprint) -DeleteKey -ErrorAction SilentlyContinue
}

if ($LASTEXITCODE -ne 0) {
    exit 1
}

Write-Host "Build complete. The MSIX package can be found in the 'AppPackages' directory."
